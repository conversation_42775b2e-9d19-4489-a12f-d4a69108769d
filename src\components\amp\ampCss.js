export const ampCSS = `
.story-top {
    padding: 2rem 0 0;
    position: sticky;
    top: 50px;
    display: flex;
    justify-content: center;
    flex-direction: column;
    z-index: 9;
}
.container {
    padding-right: 20px;
    padding-left: 20px;
}
.story-top h1 {
    font-size: 30px;
    line-height: 1;
    margin-bottom: 0;
    font-weight: 400;
    display: block;
    color: #000;
    font-style: normal;
}
`;

export const ampNavbarCSS = `
    .nav-wrapper {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem;
      background: white;
      position: relative;
    }
    .logo {
      font-size: 1.5rem;
      font-weight: bold;
      text-decoration: none;
      color: #000;
    }
    .hamburger {
      display: flex;
      flex-direction: column;
      cursor: pointer;
      padding: 0.5rem;
    }
    .hamburger .line {
      width: 25px;
      height: 3px;
      background-color: #000;
      margin: 3px 0;
      transition: 0.3s;
    }
    .hamburger.is-active .line:nth-child(1) {
      transform: rotate(-45deg) translate(-5px, 6px);
    }
    .hamburger.is-active .line:nth-child(2) {
      opacity: 0;
    }
    .hamburger.is-active .line:nth-child(3) {
      transform: rotate(45deg) translate(-5px, -6px);
    }
    .mob-menu {
      background: white;
      padding: 1rem;
      width: 300px;
    }
    .mob-item {
      display: block;
      padding: 1rem 0;
      border-bottom: 1px solid #eee;
      text-decoration: none;
      color: #212529;
      cursor: pointer;
      position: relative;
    }
    .mob-item .chevron {
      position: absolute;
      right: 0;
      font-size: 1.2rem;
    }
    .mob-submenu {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: white;
      z-index: 10;
      padding:0 20px;
    }
    .mbody-head {
      padding: 1rem 0;
      border-bottom: 1px solid #eee;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }
    .mbody-head .chevron {
      font-size: 1.2rem;
    }
    .mbody-head .title {
      font-weight: bold;
    }
    .mob-follows {
      font-weight: bold;
      margin: 1rem 0 0.5rem 0;
    }
    .mf-icons {
      display: flex;
      gap: 1rem;
    }
    .mf-icons a {
      color: black;
      font-size: 1.2rem;
    }
    .hidden {
      display: none;
    }
  `;

export const webStoryDetailCSS = `
				
				.brand-logo {
					position: absolute;
					top: 0px;
					left: 20px;
					color: white;
					text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
					z-index: 10;
				}

				.back-button {
					position: absolute;
					top: 20px;
					right: 20px;
					z-index: 10;
				}

				.back-link {
					color: white;
					text-decoration: none;
					background: rgba(0, 0, 0, 0.5);
					padding: 8px 12px;
					border-radius: 20px;
					font-size: 14px;
					text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
					transition: background 0.3s ease;
					border: none;
					cursor: pointer;
					font-family: inherit;
				}
        .next-story-preview{
          padding-bottom: 2rem;
        }

				.back-link:hover {
					background: rgba(0, 0, 0, 0.7);
				}

				.story-content {
					position: absolute;
					bottom: 0;
					left: 0;
					right: 0;
					padding: 20px;
					background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0) 34%, rgba(0, 0, 0, 1) 100%);
				}

				.story-text {
					color: white;
					text-align: left;
					font-family: NeueHaasDisplayLight, sans-serif;
				}

				.story-text h1 {
					font-size: 24px;
					font-family: NeueHaasDisplayLight, sans-serif;
					margin-bottom: 10px;
					font-weight: 400;
					line-height: 1.1;
				}

				.story-text div {
					font-family: NeueHaasDisplayLight, sans-serif;
					font-size: 16px;
					line-height: 1.3;
					font-weight: 300;
				}

				.story-text p {
					font-family: NeueHaasDisplayLight, sans-serif;
					font-size: 16px;
					line-height: 1.3;
					font-weight: 300;
				}
        .story-text p a{
        color: #fff;
        }

				.story-text small {
					display: block;
					margin-top: 8px;
					opacity: 0.8;
					font-family: NeueHaasDisplayLight, sans-serif;
					font-size: 12px;
					font-weight: 300;
				}
                [template=vertical]{
                align-content: end;
                }

        .next-story-preview {
          color: #fff
        }

					.ad-background {
						background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
						width: 100%;
						height: 100%;
					}

					.ad-content {
						display: flex;
						align-items: flex-start;
						justify-content: center;
						height: 100%;
						padding: 20px;
					}

					.ad-label {
						color: white;
						font-size: 14px;
						font-family: NeueHaasDisplayLight, sans-serif;
						background: rgba(0, 0, 0, 0.7);
						padding: 8px 16px;
						border-radius: 20px;
						margin-top: 20px;
					}

				/* AMP story navigation styles */
				.amp-story-button-container {
					display: block !important;
					visibility: visible !important;
					opacity: 1 !important;
				}

				.amp-story-button-move {
					display: block !important;
					visibility: visible !important;
					opacity: 1 !important;
				}

				@media (max-width: 768px) {
					.amp-story-button-container {
						display: block !important;
						visibility: visible !important;
						opacity: 1 !important;
					}
				}
			
			`;
